<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Microphone Permission</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1e293b;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      .container {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 24px;
        padding: 48px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        text-align: center;
        max-width: 480px;
        width: 100%;
        backdrop-filter: blur(10px);
      }
      .logo {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        padding: 12px 20px;
      }
      .logo-text {
        color: white;
        font-weight: bold;
        font-size: 18px;
        white-space: nowrap;
      }
      .mic-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 32px;
        box-shadow: 0 8px 16px rgba(6, 182, 212, 0.3);
        animation: pulse 2s infinite;
      }
      .mic-svg {
        width: 36px;
        height: 36px;
        color: white;
      }
      h1 {
        color: #1e293b;
        margin-bottom: 16px;
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(135deg, #1e293b, #475569);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      p {
        margin-bottom: 32px;
        line-height: 1.6;
        color: #64748b;
        font-size: 16px;
      }
      button {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        border: none;
        padding: 16px 32px;
        border-radius: 16px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }
      button:hover {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
      }
      button:disabled {
        background: #e2e8f0;
        color: #94a3b8;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
      .success {
        color: #059669;
        font-weight: 600;
      }
      .error {
        color: #dc2626;
        font-weight: 600;
      }
      @keyframes pulse {
        0% {
          transform: scale(1);
          box-shadow: 0 8px 16px rgba(6, 182, 212, 0.3);
        }
        50% {
          transform: scale(1.05);
          box-shadow: 0 12px 24px rgba(6, 182, 212, 0.4);
        }
        100% {
          transform: scale(1);
          box-shadow: 0 8px 16px rgba(6, 182, 212, 0.3);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">
        <span class="logo-text">Envent Bridge</span>
      </div>
      <div class="mic-icon">
        <svg class="mic-svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
        </svg>
      </div>
      <h1>Enable Voice Input</h1>
      <p>Envent Bridge needs microphone access to convert your speech to text and provide seamless voice interaction.</p>
      <button id="requestPermission">
        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
        </svg>
        Grant Microphone Permission
      </button>
      <p id="status"></p>
    </div>
    <script src="permission.js"></script>
  </body>
</html>
