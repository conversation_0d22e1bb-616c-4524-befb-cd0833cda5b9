import { BasePrompt } from './base';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import type { AgentContext } from '@src/background/agent/types';
import { exporterSystemPromptTemplate } from './templates/exporter';

export class ExporterPrompt extends BasePrompt {
  constructor() {
    super();
  }

  getSystemMessage(): SystemMessage {
    return new SystemMessage(exporterSystemPromptTemplate);
  }

  /**
   * Get the user message for the exporter prompt
   * @param context - The agent context
   * @returns The user message
   */
  async getUserMessage(context: AgentContext): Promise<HumanMessage> {
    // Get current extraction data and user request
    const extractedData = context.lastExtractionResult;
    const latestActionResult = context.actionResults[context.actionResults.length - 1];
    
    let userContent = '';
    
    // Add extraction data context
    if (extractedData && extractedData.data && extractedData.data.length > 0) {
      userContent += `AVAILABLE EXTRACTED DATA:\n`;
      userContent += `- Total items: ${extractedData.data.length}\n`;
      userContent += `- Source: ${extractedData.metadata?.source_url || 'Unknown'}\n`;
      userContent += `- Extraction method: ${extractedData.metadata?.extraction_method || 'Unknown'}\n`;
      userContent += `- Sample data (first 3 items):\n`;
      userContent += JSON.stringify(extractedData.data.slice(0, 3), null, 2);
      userContent += '\n\n';
    } else {
      userContent += `NO EXTRACTED DATA AVAILABLE - User may be requesting export without prior extraction.\n\n`;
    }
    
    // Add recent action context
    if (latestActionResult && latestActionResult.extractedContent) {
      userContent += `LATEST ACTION RESULT:\n${latestActionResult.extractedContent}\n\n`;
    }
    
    // Add user request from message history
    const messages = context.messageManager.getMessages();
    const latestUserMessage = messages.filter(msg => msg.constructor.name === 'HumanMessage').pop();
    if (latestUserMessage) {
      userContent += `USER REQUEST:\n${latestUserMessage.content}\n\n`;
    }
    
    userContent += `Please analyze the request and available data to determine the appropriate export action.`;
    
    return new HumanMessage(userContent);
  }
} 